# PTS Renamer Frontend Module

這個模組提供 PTS（Parametric Test Suite）檔案重命名工具的網頁介面。

## 🎯 功能概覽

### 核心功能
- **檔案上傳**: 支援拖放上傳壓縮檔案（ZIP、7Z、RAR）
- **批量重命名**: 基於模式的智能檔案重命名
- **QC 檔案生成**: 自動生成品質控制副本
- **目錄組織**: 自動創建分類目錄結構
- **即時預覽**: 處理前預覽所有變更
- **進度追蹤**: 即時顯示處理進度
- **結果下載**: 處理完成後下載結果

### ✅ 實現狀態 (2025-01-20)
- **HTML 模板**: ✅ 完成現代化響應式介面
- **CSS 樣式**: ✅ 完成模組化樣式系統
- **JavaScript 邏輯**: ✅ 完成前端互動邏輯
- **Flask 路由**: ✅ 已存在並整合到主應用
- **錯誤處理**: ✅ 完成統一錯誤頁面
- **系統整合**: ✅ 與現有導航和樣式系統完美整合

## Web Access

**URL**: `http://localhost:5000/pts-renamer/`

## Architecture

### Flask Blueprint Structure

```
frontend/pts_renamer/
├── routes/                # Flask route handlers
│   ├── pts_rename_flask_routes.py
│   └── __init__.py
├── templates/             # HTML templates
│   ├── pts_rename_main.html
│   └── __init__.py
├── static/                # Static assets
│   ├── js/
│   │   ├── pts_rename_upload.js
│   │   └── pts_rename_processor.js
│   ├── css/
│   │   └── pts_rename_styles.css
│   └── __init__.py
├── components/            # Reusable components
│   └── __init__.py
├── README.md
└── __init__.py
```

## Features

### User Interface Components

1. **File Upload Interface**
   - Drag-and-drop file upload
   - Multiple file selection
   - Progress indicators
   - File validation feedback

2. **Processing Configuration**
   - Operation selection (rename, QC, directories)
   - Pattern configuration for renaming
   - Real-time preview
   - Validation feedback

3. **Progress Monitoring**
   - Real-time job status updates
   - Progress bars and indicators
   - Error reporting
   - Completion notifications

4. **Results Management**
   - Download links for processed files
   - Processing summaries
   - Error logs and warnings

### API Endpoints

#### Upload Endpoints
- `POST /pts-renamer/api/upload` - Handle file uploads
- `GET /pts-renamer/api/upload/status/<upload_id>` - Upload status

#### Processing Endpoints
- `POST /pts-renamer/api/process` - Start processing job
- `GET /pts-renamer/api/status/<job_id>` - Get job status
- `POST /pts-renamer/api/preview` - Generate processing preview

#### Download Endpoints
- `GET /pts-renamer/api/download/<job_id>` - Download results
- `GET /pts-renamer/api/download/<job_id>/direct` - Direct file download

#### Utility Endpoints
- `GET /pts-renamer/health` - Health check
- `GET /pts-renamer/` - Main interface

## Templates

### Main Template (`pts_rename_main.html`)

The main template provides:
- Modern, responsive design
- File upload area with drag-and-drop
- Processing configuration panel
- Preview section
- Progress monitoring
- Results display

Template structure:
```html
<!DOCTYPE html>
<html>
<head>
    <title>PTS File Renamer</title>
    <!-- CSS includes -->
</head>
<body>
    <div class="pts-renamer-container">
        <header><!-- Navigation --></header>
        <main>
            <section class="upload-section"><!-- File upload --></section>
            <section class="config-section"><!-- Configuration --></section>
            <section class="preview-section"><!-- Preview --></section>
            <section class="progress-section"><!-- Progress --></section>
            <section class="results-section"><!-- Results --></section>
        </main>
    </div>
    <!-- JavaScript includes -->
</body>
</html>
```

## JavaScript Components

### Upload Handler (`pts_rename_upload.js`)

Handles file upload functionality:
- Drag-and-drop events
- File validation
- Upload progress tracking
- Error handling

Key functions:
```javascript
class PTSUploadHandler {
    constructor(uploadArea, progressCallback);
    handleDrop(event);
    handleFileSelect(files);
    uploadFiles(files);
    validateFiles(files);
    updateProgress(progress);
}
```

### Processor Interface (`pts_rename_processor.js`)

Manages processing configuration and monitoring:
- Configuration form handling
- Preview generation
- Job status polling
- Results display

Key functions:
```javascript
class PTSProcessorInterface {
    constructor(configForm, previewArea, statusArea);
    generatePreview(config);
    startProcessing(config);
    pollJobStatus(jobId);
    displayResults(results);
}
```

## CSS Styling

### Main Stylesheet (`pts_rename_styles.css`)

Provides modern, responsive styling:
- Consistent with existing system design
- Mobile-friendly responsive layout
- Accessibility compliance
- Dark/light theme support

Key style classes:
```css
.pts-renamer-container { /* Main container */ }
.upload-area { /* File upload zone */ }
.config-panel { /* Configuration panel */ }
.preview-table { /* Preview display */ }
.progress-bar { /* Progress indicators */ }
.results-grid { /* Results layout */ }
```

## Integration

### Backend Integration

The frontend integrates with backend services through:
- **Presenter Layer**: `PTSRenamePresenter` handles all business logic
- **Service Layer**: Core services process files and manage data
- **Repository Layer**: Data persistence and retrieval

### Shared Components

Uses existing shared frontend components:
- Base templates from `frontend/shared/templates/`
- Common CSS from `frontend/shared/static/css/`
- Shared JavaScript utilities from `frontend/shared/static/js/`

### Error Handling

Implements comprehensive error handling:
- Client-side validation
- Server error display
- User-friendly error messages
- Retry mechanisms

## Configuration

### Flask Blueprint Registration

```python
from frontend.pts_renamer import pts_renamer_bp

app.register_blueprint(pts_renamer_bp)
```

### Template Configuration

Templates extend the base template:
```html
{% extends "base.html" %}
{% block title %}PTS File Renamer{% endblock %}
{% block content %}
<!-- PTS Renamer content -->
{% endblock %}
```

## Development Guidelines

### File Naming Convention
- Templates: `pts_rename_*.html`
- JavaScript: `pts_rename_*.js`
- CSS: `pts_rename_*.css`
- Routes: `pts_rename_*_routes.py`

### Code Standards
- ES6+ JavaScript features
- Responsive CSS design
- Accessible HTML markup
- Flask blueprint patterns

### Testing
- Unit tests for JavaScript functions
- Integration tests for API endpoints
- E2E tests for user workflows

## API Request/Response Formats

### Upload Request
```javascript
// FormData with files
const formData = new FormData();
formData.append('files', file1);
formData.append('files', file2);
```

### Upload Response
```json
{
    "success": true,
    "upload_id": "upload_123",
    "files_uploaded": 2,
    "pts_files_found": 2,
    "message": "Upload completed successfully"
}
```

### Processing Request
```json
{
    "upload_id": "upload_123",
    "operations": ["rename", "qc_generation"],
    "rename_config": {
        "old_pattern": "test_*.pts",
        "new_pattern": "device_{num}_renamed.pts"
    },
    "qc_enabled": true,
    "create_directories": false
}
```

### Processing Response
```json
{
    "success": true,
    "job_id": "pts_job_abc123",
    "status": "pending",
    "message": "Processing job queued successfully"
}
```

### Status Response
```json
{
    "success": true,
    "job_id": "pts_job_abc123",
    "status": "processing",
    "progress": 45,
    "files_processed": 9,
    "total_files": 20,
    "download_url": null
}
```

### Preview Response
```json
{
    "success": true,
    "upload_id": "upload_123",
    "total_files": 2,
    "files_preview": [
        {
            "original_name": "test_001.pts",
            "new_name": "device_001_renamed.pts",
            "qc_file_name": "device_001_renamed_QC.pts",
            "operations_applied": ["rename", "qc_generation"],
            "warnings": []
        }
    ],
    "operations_summary": {
        "rename": 2,
        "qc_generation": 2
    },
    "estimated_processing_time": 30
}
```

## Error Handling

### Client-Side Errors
- File validation errors
- Network connectivity issues
- Invalid configuration

### Server-Side Errors
- Upload failures
- Processing errors
- System errors

### Error Display
```javascript
function displayError(error) {
    const errorDiv = document.getElementById('error-display');
    errorDiv.innerHTML = `
        <div class="alert alert-danger">
            <strong>Error:</strong> ${error.message}
            <details>${error.details}</details>
        </div>
    `;
}
```

## Future Migration

### Vue.js Preparation
The current Flask templates are designed to facilitate Vue.js migration:
- Component-based structure
- Separation of concerns
- API-first approach
- Modern JavaScript patterns

### Migration Path
1. **Phase 1**: Current Flask implementation
2. **Phase 2**: Hybrid Flask + Vue components
3. **Phase 3**: Full Vue.js SPA with FastAPI backend

## Performance Optimization

### Client-Side
- Lazy loading of components
- File chunking for large uploads
- Progress streaming
- Caching of configuration

### Server-Side
- Async request handling
- Background job processing
- Result compression
- Automatic cleanup

## Accessibility

The interface follows WCAG 2.1 guidelines:
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Focus management

## Browser Support

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Dependencies

### JavaScript Libraries
- Native ES6+ (no external dependencies)
- Fetch API for HTTP requests
- FormData for file uploads

### CSS Framework
- Custom CSS based on existing system styles
- CSS Grid and Flexbox for layout
- CSS Variables for theming

## Troubleshooting

### Common Issues
1. **Upload failures**: Check file size and format
2. **Processing stuck**: Check Dramatiq queue status
3. **Download not available**: Verify job completion
4. **Preview errors**: Validate rename patterns

### Debug Mode
Enable debug logging:
```javascript
window.PTS_DEBUG = true;
```

## Support

For development support:
- Backend integration: See `backend/pts_renamer/README.md`
- API documentation: Check route definitions
- Error logs: Monitor Flask application logs