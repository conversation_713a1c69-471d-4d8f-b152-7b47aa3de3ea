/**
 * PTS Renamer JavaScript 模組
 * 提供 PTS 檔案重命名工具的前端邏輯
 */

class PTSRenamerAPI {
    constructor() {
        this.baseUrl = '/pts-renamer/api';
        this.timeout = 30000; // 30 秒超時
    }

    async uploadFiles(files) {
        const formData = new FormData();
        files.forEach(file => formData.append('files', file));

        const response = await fetch(`${this.baseUrl}/upload`, {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`上傳失敗: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    async processFiles(config) {
        const response = await fetch(`${this.baseUrl}/process`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        });

        if (!response.ok) {
            throw new Error(`處理失敗: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    async getJobStatus(jobId) {
        const response = await fetch(`${this.baseUrl}/status/${jobId}`);

        if (!response.ok) {
            throw new Error(`狀態查詢失敗: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    async getPreview(config) {
        const response = await fetch(`${this.baseUrl}/preview`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        });

        if (!response.ok) {
            throw new Error(`預覽生成失敗: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    async downloadResults(jobId) {
        const response = await fetch(`${this.baseUrl}/download/${jobId}`);

        if (!response.ok) {
            throw new Error(`下載失敗: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }
}

class PTSRenamerUI {
    constructor() {
        this.api = new PTSRenamerAPI();
        this.uploadedFiles = [];
        this.isProcessing = false;
        this.currentJobId = null;
        this.progressInterval = null;
        this.notifications = [];
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateUI();
        this.initializeTooltips();
    }

    bindEvents() {
        // 檔案上傳事件
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        }
        
        if (uploadArea) {
            // 拖放事件
            uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
            uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
            uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
            uploadArea.addEventListener('dragenter', (e) => this.handleDragEnter(e));
        }
        
        // 選項切換事件
        ['renameEnabled', 'qcEnabled', 'createDirectories'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.toggleOptions());
            }
        });
        
        // 按鈕事件
        this.bindButtonEvents();
        
        // 鍵盤快捷鍵
        this.bindKeyboardEvents();
    }

    bindButtonEvents() {
        const buttons = {
            'clearFilesBtn': () => this.clearUploadedFiles(),
            'previewBtn': () => this.previewProcessing(),
            'clearPreviewBtn': () => this.clearPreview(),
            'executeBtn': () => this.executeProcessing()
        };

        Object.entries(buttons).forEach(([id, handler]) => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('click', handler);
            }
        });
    }

    bindKeyboardEvents() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+U: 上傳檔案
            if (e.ctrlKey && e.key === 'u') {
                e.preventDefault();
                document.getElementById('fileInput')?.click();
            }
            
            // Ctrl+P: 預覽
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                this.previewProcessing();
            }
            
            // Ctrl+Enter: 開始處理
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                if (!this.isProcessing) {
                    this.executeProcessing();
                }
            }
            
            // ESC: 清除選擇
            if (e.key === 'Escape') {
                this.clearPreview();
            }
        });
    }

    initializeTooltips() {
        // 初始化工具提示（如果需要）
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', this.showTooltip.bind(this));
            element.addEventListener('mouseleave', this.hideTooltip.bind(this));
        });
    }

    showTooltip(event) {
        const element = event.target;
        const text = element.getAttribute('data-tooltip');
        
        if (!text) return;
        
        const tooltip = document.createElement('div');
        tooltip.className = 'pts-tooltip';
        tooltip.textContent = text;
        tooltip.style.cssText = `
            position: absolute;
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            pointer-events: none;
        `;
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
        
        element._tooltip = tooltip;
    }

    hideTooltip(event) {
        const element = event.target;
        if (element._tooltip) {
            document.body.removeChild(element._tooltip);
            delete element._tooltip;
        }
    }

    handleFileSelect(event) {
        const files = Array.from(event.target.files);
        this.addFiles(files);
    }

    handleDragOver(event) {
        event.preventDefault();
        event.stopPropagation();
    }

    handleDragEnter(event) {
        event.preventDefault();
        event.stopPropagation();
        document.getElementById('uploadArea')?.classList.add('drag-enter');
    }

    handleDragLeave(event) {
        event.preventDefault();
        event.stopPropagation();
        
        // 只有當離開整個上傳區域時才移除樣式
        if (!event.currentTarget.contains(event.relatedTarget)) {
            document.getElementById('uploadArea')?.classList.remove('drag-enter', 'drag-over');
        }
    }

    handleDrop(event) {
        event.preventDefault();
        event.stopPropagation();
        
        const uploadArea = document.getElementById('uploadArea');
        uploadArea?.classList.remove('drag-enter', 'drag-over');
        
        const files = Array.from(event.dataTransfer.files);
        this.addFiles(files);
    }

    addFiles(files) {
        const validExtensions = ['.zip', '.7z', '.rar'];
        const maxFileSize = 100 * 1024 * 1024; // 100MB
        let addedCount = 0;
        
        files.forEach(file => {
            const extension = file.name.toLowerCase().slice(file.name.lastIndexOf('.'));
            
            // 檢查檔案格式
            if (!validExtensions.includes(extension)) {
                this.showNotification(`檔案 "${file.name}" 格式不支援`, 'error');
                return;
            }
            
            // 檢查檔案大小
            if (file.size > maxFileSize) {
                this.showNotification(`檔案 "${file.name}" 太大（超過 100MB）`, 'error');
                return;
            }
            
            // 檢查是否已存在
            if (!this.uploadedFiles.find(f => f.name === file.name && f.size === file.size)) {
                this.uploadedFiles.push({
                    file: file,
                    name: file.name,
                    size: file.size,
                    type: this.getFileType(file.name),
                    uploadTime: new Date(),
                    id: this.generateFileId()
                });
                addedCount++;
            }
        });
        
        if (addedCount > 0) {
            this.updateUploadedFilesList();
            this.updateUI();
            this.showNotification(`成功添加 ${addedCount} 個檔案`, 'success');
            this.animateFileAddition();
        }
    }

    generateFileId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    getFileType(fileName) {
        const extension = fileName.toLowerCase().slice(fileName.lastIndexOf('.'));
        switch (extension) {
            case '.zip': return 'ZIP';
            case '.7z': return '7Z';
            case '.rar': return 'RAR';
            default: return 'UNKNOWN';
        }
    }

    getFileIcon(type) {
        switch (type) {
            case 'ZIP': return 'fas fa-file-archive';
            case '7Z': return 'fas fa-compress-arrows-alt';
            case 'RAR': return 'fas fa-archive';
            default: return 'fas fa-file';
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    updateUploadedFilesList() {
        const container = document.getElementById('uploadedFiles');
        const clearBtn = document.getElementById('clearFilesBtn');
        
        if (!container) return;
        
        if (this.uploadedFiles.length === 0) {
            container.innerHTML = '';
            clearBtn?.classList.add('hidden');
        } else {
            container.innerHTML = this.uploadedFiles.map((fileData, index) => `
                <div class="file-item pts-fade-in" data-file-id="${fileData.id}">
                    <div class="file-info">
                        <div class="file-icon ${fileData.type.toLowerCase()}">
                            <i class="${this.getFileIcon(fileData.type)}"></i>
                        </div>
                        <div class="file-details">
                            <div class="file-name">${this.escapeHtml(fileData.name)}</div>
                            <div class="file-meta">
                                ${this.formatFileSize(fileData.size)} • ${fileData.type} • 
                                ${fileData.uploadTime.toLocaleTimeString()}
                            </div>
                        </div>
                    </div>
                    <button class="remove-file-btn" onclick="ptsRenamerUI.removeUploadedFile(${index})" 
                            data-tooltip="移除此檔案">
                        <i class="fas fa-times"></i> 移除
                    </button>
                </div>
            `).join('');
            clearBtn?.classList.remove('hidden');
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    removeUploadedFile(index) {
        if (index >= 0 && index < this.uploadedFiles.length) {
            const fileItem = document.querySelector(`[data-file-id="${this.uploadedFiles[index].id}"]`);
            
            if (fileItem) {
                fileItem.classList.add('pts-fade-out');
                setTimeout(() => {
                    this.uploadedFiles.splice(index, 1);
                    this.updateUploadedFilesList();
                    this.clearPreview();
                    this.updateUI();
                    this.showNotification('檔案已移除', 'info');
                }, 300);
            } else {
                this.uploadedFiles.splice(index, 1);
                this.updateUploadedFilesList();
                this.clearPreview();
                this.updateUI();
            }
        }
    }

    clearUploadedFiles() {
        this.uploadedFiles = [];
        this.updateUploadedFilesList();
        this.clearPreview();
        this.hideDownloadSection();
        this.updateUI();
        this.showNotification('所有檔案已清除', 'info');
    }

    toggleOptions() {
        const renameEnabled = document.getElementById('renameEnabled')?.checked || false;
        const qcEnabled = document.getElementById('qcEnabled')?.checked || false;
        const createDirectories = document.getElementById('createDirectories')?.checked || false;

        const optionElements = {
            'renameOptions': renameEnabled,
            'qcOptions': qcEnabled,
            'directoryOptions': createDirectories
        };

        Object.entries(optionElements).forEach(([id, show]) => {
            const element = document.getElementById(id);
            if (element) {
                element.classList.toggle('hidden', !show);
                if (show) {
                    element.classList.add('pts-fade-in');
                }
            }
        });
        
        this.updateUI();
        this.clearPreview(); // 選項變更時清除預覽
    }

    updateUI() {
        const executeBtn = document.getElementById('executeBtn');
        if (!executeBtn) return;
        
        const hasFiles = this.uploadedFiles.length > 0;
        const hasOptions = ['renameEnabled', 'qcEnabled', 'createDirectories']
            .some(id => document.getElementById(id)?.checked);
        
        const canExecute = hasFiles && hasOptions && !this.isProcessing;
        executeBtn.disabled = !canExecute;
        
        // 更新按鈕樣式
        if (canExecute) {
            executeBtn.classList.remove('disabled');
            executeBtn.title = '';
        } else {
            executeBtn.classList.add('disabled');
            if (!hasFiles) {
                executeBtn.title = '請先上傳檔案';
            } else if (!hasOptions) {
                executeBtn.title = '請選擇至少一個處理選項';
            } else if (this.isProcessing) {
                executeBtn.title = '處理中，請稍候';
            }
        }
    }

    animateFileAddition() {
        const fileItems = document.querySelectorAll('.file-item:last-child');
        fileItems.forEach(item => {
            item.classList.add('pts-bounce');
            setTimeout(() => item.classList.remove('pts-bounce'), 600);
        });
    }

    async previewProcessing() {
        if (this.uploadedFiles.length === 0) {
            this.showNotification('請先上傳至少一個檔案', 'error');
            return;
        }

        const operations = this.getSelectedOperations();
        if (operations.length === 0) {
            this.showNotification('請至少選擇一個處理選項', 'error');
            return;
        }

        try {
            this.showProcessingOverlay('previewList');
            
            // 在實際應用中，這裡會調用 API
            // const result = await this.api.getPreview({
            //     upload_id: 'preview_' + Date.now(),
            //     operations: operations,
            //     rename_config: this.getRenameConfig()
            // });
            
            // 模擬 API 延遲
            await this.delay(1000);
            
            this.generatePreview(operations);
            this.showNotification('預覽生成完成', 'success');
        } catch (error) {
            console.error('預覽生成失敗:', error);
            this.showNotification('預覽生成失敗: ' + error.message, 'error');
        } finally {
            this.hideProcessingOverlay('previewList');
        }
    }

    showProcessingOverlay(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        const overlay = document.createElement('div');
        overlay.className = 'processing-overlay';
        overlay.innerHTML = `
            <div class="processing-spinner"></div>
            <div class="processing-text">處理中...</div>
        `;
        
        container.style.position = 'relative';
        container.appendChild(overlay);
    }

    hideProcessingOverlay(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        const overlay = container.querySelector('.processing-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    generatePreview(operations) {
        const previewList = document.getElementById('previewList');
        if (!previewList) return;
        
        let previewHTML = '<div class="preview-item info"><span class="status-icon"><i class="fas fa-info-circle"></i></span>=== 處理預覽 ===</div>';

        this.uploadedFiles.forEach((fileData, index) => {
            previewHTML += `<div class="preview-item info file-added"><span class="status-icon"><i class="fas fa-file-archive"></i></span>處理壓縮檔：${this.escapeHtml(fileData.name)}</div>`;

            // 模擬檔案內容
            const sampleFiles = [
                `test_${index + 1}_001.pts`,
                `device_data_${index + 1}_002.pts`,
                `quality_check_${index + 1}_003.pts`
            ];

            sampleFiles.forEach(file => {
                if (operations.includes('rename')) {
                    const oldPattern = document.getElementById('oldPattern')?.value || '';
                    const newPattern = document.getElementById('newPattern')?.value || '';

                    if (oldPattern && newPattern) {
                        const newName = this.applyRenamePattern(file, oldPattern, newPattern);
                        previewHTML += `<div class="preview-item success file-renamed"><span class="status-icon"><i class="fas fa-check"></i></span>　重命名：${file} → ${newName}</div>`;
                    } else {
                        previewHTML += `<div class="preview-item warning"><span class="status-icon"><i class="fas fa-exclamation-triangle"></i></span>　重命名：請設定重命名模式</div>`;
                    }
                }

                if (operations.includes('qc_generation')) {
                    const qcSuffix = document.getElementById('qcSuffix')?.value || '_QC';
                    const qcName = file.replace('.pts', `${qcSuffix}.pts`);
                    previewHTML += `<div class="preview-item success file-qc"><span class="status-icon"><i class="fas fa-check"></i></span>　QC檔案：${file} → ${qcName}</div>`;
                }

                if (operations.includes('directory_creation')) {
                    const dirName = file.replace('.pts', '');
                    previewHTML += `<div class="preview-item success"><span class="status-icon"><i class="fas fa-folder"></i></span>　創建目錄：${dirName}/</div>`;
                }
            });

            previewHTML += '<div class="preview-item"><span class="status-icon"></span>&nbsp;</div>';
        });

        previewList.innerHTML = previewHTML;
        
        // 添加動畫效果
        const items = previewList.querySelectorAll('.preview-item');
        items.forEach((item, index) => {
            setTimeout(() => {
                item.classList.add('pts-fade-in');
            }, index * 50);
        });
    }

    applyRenamePattern(filename, oldPattern, newPattern) {
        const baseName = filename.replace('.pts', '');
        const extension = '.pts';
        
        let result = newPattern;
        result = result.replace(/\{old\}/g, baseName);
        result = result.replace(/\{ext\}/g, extension);
        result = result.replace(/\{num\}/g, Math.floor(Math.random() * 1000).toString().padStart(3, '0'));
        
        if (oldPattern && filename.includes(oldPattern)) {
            result = filename.replace(new RegExp(oldPattern, 'g'), newPattern);
        }
        
        return result + (result.endsWith('.pts') ? '' : '.pts');
    }

    clearPreview() {
        const previewList = document.getElementById('previewList');
        if (!previewList) return;
        
        previewList.innerHTML = `
            <div class="preview-empty">
                <i class="fas fa-info-circle" style="margin-right: 8px;"></i>
                請上傳檔案並選擇處理選項後，點擊「預覽處理結果」查看效果
            </div>
        `;
    }

    getSelectedOperations() {
        const operations = [];
        if (document.getElementById('renameEnabled')?.checked) operations.push('rename');
        if (document.getElementById('qcEnabled')?.checked) operations.push('qc_generation');
        if (document.getElementById('createDirectories')?.checked) operations.push('directory_creation');
        return operations;
    }

    getRenameConfig() {
        return {
            old_pattern: document.getElementById('oldPattern')?.value || '',
            new_pattern: document.getElementById('newPattern')?.value || ''
        };
    }

    async executeProcessing() {
        if (this.isProcessing) return;

        const operations = this.getSelectedOperations();
        if (this.uploadedFiles.length === 0 || operations.length === 0) {
            this.showNotification('請確認已上傳檔案並選擇處理選項', 'error');
            return;
        }

        // 驗證重命名設定
        if (operations.includes('rename')) {
            const oldPattern = document.getElementById('oldPattern')?.value;
            const newPattern = document.getElementById('newPattern')?.value;
            if (!oldPattern || !newPattern) {
                this.showNotification('啟用重命名時，請輸入重命名模式', 'error');
                return;
            }
        }

        try {
            await this.startProcessing(operations);
        } catch (error) {
            console.error('處理失敗:', error);
            this.showNotification('處理失敗：' + error.message, 'error');
            this.stopProcessing();
        }
    }

    async startProcessing(operations) {
        this.isProcessing = true;
        this.updateProcessingUI(true);

        try {
            // 在實際應用中會調用真實 API
            // const uploadResult = await this.api.uploadFiles(this.uploadedFiles.map(f => f.file));
            // const processResult = await this.api.processFiles({
            //     upload_id: uploadResult.upload_id,
            //     operations: operations,
            //     rename_config: this.getRenameConfig(),
            //     qc_enabled: operations.includes('qc_generation'),
            //     create_directories: operations.includes('directory_creation')
            // });
            // this.currentJobId = processResult.job_id;
            
            // 模擬處理過程
            await this.simulateProcessing();
            
        } catch (error) {
            throw error;
        }
    }

    async simulateProcessing() {
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const progressBar = document.querySelector('.progress-bar');
        
        if (progressBar) {
            progressBar.classList.add('processing');
        }
        
        let progress = 0;
        
        return new Promise((resolve) => {
            const interval = setInterval(() => {
                progress += Math.random() * 10 + 5;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    this.finishProcessing();
                    resolve();
                }

                if (progressFill) {
                    progressFill.style.width = progress + '%';
                    progressFill.textContent = Math.round(progress) + '%';
                    progressFill.classList.add('animated');
                }
                
                if (progressText) {
                    progressText.textContent = `處理中... ${Math.round(progress)}%`;
                }
            }, 300);

            this.progressInterval = interval;
        });
    }

    updateProcessingUI(processing) {
        const executeBtn = document.getElementById('executeBtn');
        const progressSection = document.getElementById('progressSection');
        
        if (!executeBtn || !progressSection) return;

        if (processing) {
            executeBtn.innerHTML = '<span class="status-indicator status-processing"></span><i class="fas fa-spinner fa-spin"></i>處理中...';
            executeBtn.disabled = true;
            progressSection.classList.remove('hidden');
        } else {
            executeBtn.innerHTML = '<span class="status-indicator status-ready"></span><i class="fas fa-play"></i>開始處理';
            executeBtn.disabled = false;
            progressSection.classList.add('hidden');
        }
    }

    finishProcessing() {
        const executeBtn = document.getElementById('executeBtn');
        const progressText = document.getElementById('progressText');
        const progressBar = document.querySelector('.progress-bar');

        // 顯示完成狀態
        if (executeBtn) {
            executeBtn.innerHTML = '<span class="status-indicator status-ready"></span><i class="fas fa-check"></i>處理完成！';
            executeBtn.classList.add('success-glow');
        }
        
        if (progressText) {
            progressText.textContent = '處理完成！';
        }
        
        if (progressBar) {
            progressBar.classList.remove('processing');
            progressBar.classList.add('success-glow');
        }

        // 生成結果摘要
        this.generateProcessingResult();

        // 顯示下載區域
        setTimeout(() => {
            this.showDownloadSection();
        }, 1000);

        // 重置按鈕狀態
        setTimeout(() => {
            this.stopProcessing();
        }, 3000);
        
        this.showNotification('檔案處理完成！', 'success');
    }

    stopProcessing() {
        this.isProcessing = false;
        
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }

        this.updateProcessingUI(false);
        this.updateUI();
        
        // 移除成功發光效果
        setTimeout(() => {
            const elements = document.querySelectorAll('.success-glow');
            elements.forEach(el => el.classList.remove('success-glow'));
        }, 2000);
    }

    generateProcessingResult() {
        const previewList = document.getElementById('previewList');
        if (!previewList) return;
        
        const totalFiles = this.uploadedFiles.length * 3; // 每個檔案模擬包含3個.pts檔案
        const operations = this.getSelectedOperations();

        let resultHTML = '<div class="preview-item info"><span class="status-icon"><i class="fas fa-check-circle"></i></span>=== 處理完成 ===</div>';
        resultHTML += `<div class="preview-item success"><span class="status-icon"><i class="fas fa-check"></i></span>成功處理：${totalFiles} 個 PTS 檔案</div>`;
        resultHTML += '<div class="preview-item error"><span class="status-icon"><i class="fas fa-times"></i></span>處理失敗：0 個檔案</div>';
        resultHTML += '<div class="preview-item"><span class="status-icon"></span>&nbsp;</div>';
        resultHTML += '<div class="preview-item info"><span class="status-icon"><i class="fas fa-list"></i></span>=== 操作摘要 ===</div>';

        const operationNames = {
            'rename': '檔案重命名',
            'qc_generation': 'QC 檔案生成',
            'directory_creation': '目錄創建'
        };

        operations.forEach(op => {
            const opName = operationNames[op] || op;
            resultHTML += `<div class="preview-item success"><span class="status-icon"><i class="fas fa-check"></i></span>${opName}：已執行</div>`;
        });

        previewList.innerHTML = resultHTML;
    }

    showDownloadSection() {
        const downloadSection = document.getElementById('downloadSection');
        const downloadList = document.getElementById('downloadList');

        if (!downloadSection || !downloadList) return;

        downloadSection.classList.remove('hidden');
        downloadSection.classList.add('pts-fade-in');

        let downloadHTML = '';
        this.uploadedFiles.forEach((fileData, index) => {
            const processedFileName = fileData.name.replace(/\.(zip|7z|rar)$/i, '_processed.zip');
            const fileSize = Math.round(fileData.size * 1.2); // 模擬處理後檔案可能稍大
            const downloadUrl = `/pts-renamer/api/download/job_${Date.now()}_${index}`;

            downloadHTML += `
                <div class="download-item">
                    <div class="download-info">
                        <div class="download-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <div class="download-details">
                            <div class="download-name">${this.escapeHtml(processedFileName)}</div>
                            <div class="download-meta">
                                處理時間：${new Date().toLocaleString()} • 大小：${this.formatFileSize(fileSize)}
                            </div>
                        </div>
                    </div>
                    <a href="${downloadUrl}" class="download-btn" onclick="ptsRenamerUI.simulateDownload('${this.escapeHtml(processedFileName)}'); return false;"
                       data-tooltip="下載處理結果">
                        <i class="fas fa-download"></i>
                        下載
                    </a>
                </div>
            `;
        });

        downloadList.innerHTML = downloadHTML;

        // 滾動到下載區域
        setTimeout(() => {
            downloadSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }, 500);
    }

    hideDownloadSection() {
        const downloadSection = document.getElementById('downloadSection');
        if (downloadSection) {
            downloadSection.classList.add('hidden');
        }
    }

    simulateDownload(fileName) {
        this.showNotification(`開始下載：${fileName}`, 'info');
        // 在實際應用中，這裡會觸發真實的檔案下載
        console.log('模擬下載:', fileName);
    }

    showNotification(message, type = 'info') {
        // 使用現有的通知系統
        if (window.notificationManager) {
            window.notificationManager.show(message, type);
        } else {
            // 備用通知方式
            console.log(`[${type.toUpperCase()}] ${message}`);
            
            // 建立簡單的通知
            const notification = document.createElement('div');
            notification.className = `pts-notification pts-notification-${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;
            
            // 設定顏色
            const colors = {
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107',
                info: '#17a2b8'
            };
            notification.style.background = colors[type] || colors.info;
            
            document.body.appendChild(notification);
            
            // 顯示動畫
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // 自動隱藏
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
    }
}

// 全域實例
let ptsRenamerUI;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    ptsRenamerUI = new PTSRenamerUI();
    
    // 設定全域引用以便在 HTML 中使用
    window.ptsRenamer = ptsRenamerUI;
});