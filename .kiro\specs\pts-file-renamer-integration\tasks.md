# PTS File Renamer Integration - Implementation Plan

## Task Overview

Convert the existing PTS file renaming functionality into a fully integrated service using MVP architecture, preparing for Vue.js + FastAPI migration while maintaining all existing features.

## Implementation Tasks

- [x] 1. Set up PTS Renamer module structure and core interfaces





  - Create modular directory structure following existing backend patterns
  - Define MVP architecture interfaces and base classes
  - Set up integration with existing shared infrastructure
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [x] 2. Implement core data models and entities





  - [x] 2.1 Create PTS rename data models with Pydantic validation


    - Implement `pts_rename_models.py` with job request/status models
    - Create validation for file operations and processing options
    - _Requirements: 2.1, 2.2, 6.1_

  - [x] 2.2 Implement PTS business entities and value objects


    - Create `pts_rename_entities.py` with domain objects
    - Implement PTS file, processing job, and result entities
    - _Requirements: 2.1, 5.1_

  - [x] 2.3 Create repository interfaces and database models



    - Implement `pts_rename_repository.py` with data access patterns
    - Extend existing `outlook.db` with PTS processing tables
    - Use existing database connection infrastructure
    - _Requirements: 2.4, 9.1, 9.2_

- [x] 3. Implement file processing core services




  - [x] 3.1 Create PTS file renaming processor



    - Implement `pts_rename_processor.py` with pattern matching logic
    - Support regex patterns and placeholder substitution ({old}, {ext}, {num})
    - Handle file naming conflicts and validation
    - _Requirements: 5.1, 5.5_

  - [x] 3.2 Implement QC file generation service


    - Create `pts_rename_qc_generator.py` with complete QC logic
    - Remove data between "Parameter," and "QA," sections
    - Modify QCOnlySBinAlter, recalculate ParamCnt, filter Bin Definition
    - _Requirements: 5.2_

  - [x] 3.3 Create directory management service


    - Implement `pts_rename_directory_manager.py` for directory operations
    - Copy folder contents, exclude other PTS files, handle conflicts
    - _Requirements: 5.3_

- [x] 4. Implement upload and file handling services





  - [x] 4.1 Create compressed file upload service


    - Implement `pts_rename_upload_service.py` for file upload handling
    - Support ZIP, 7Z, RAR file validation and processing
    - Integrate with existing Dramatiq decompression tasks
    - _Requirements: 2.1, 2.2, 6.1, 6.2, 6.3_

  - [x] 4.2 Implement download and compression service


    - Create `pts_rename_download_service.py` for result packaging
    - Auto-compress processed files using existing compression tasks
    - Generate secure download URLs and manage file cleanup
    - _Requirements: 2.5, 9.1, 9.2, 9.3_

- [ ] 5. Implement Dramatiq integration and async processing
  - [ ] 5.1 Create Dramatiq task integration service
    - Implement `pts_rename_dramatiq_integration.py` for task management
    - Queue processing jobs using existing Dramatiq infrastructure
    - Integrate with existing compression, decompression, and batch processing tasks
    - _Requirements: 4.1, 4.2, 4.3, 10.1, 10.2, 10.3_

  - [ ] 5.2 Implement job status tracking and monitoring
    - Create job status updates and progress tracking
    - Integrate with existing monitoring dashboard
    - Handle job failures and retry mechanisms
    - _Requirements: 4.4, 4.5, 4.6, 7.2, 7.3_

- [ ] 6. Implement MVP presenter layer (business logic)
  - [ ] 6.1 Create main PTS rename presenter
    - Implement `pts_rename_presenter.py` as central business logic controller
    - Handle upload requests, processing requests, and status queries
    - Coordinate between services and manage workflow
    - _Requirements: 2.3, 2.4, 2.5, 8.1, 8.2_

  - [ ] 6.2 Implement core PTS rename service
    - Create `pts_rename_service.py` as main service orchestrator
    - Coordinate file processing, QC generation, and directory creation
    - Handle batch operations and result finalization
    - _Requirements: 5.4, 8.3, 8.4_

- [ ] 7. Implement Flask web interface (current implementation)
  - [ ] 7.1 Create Flask routes and API endpoints
    - Implement `pts_rename_flask_routes.py` with complete API
    - Support upload, processing, status, preview, and download endpoints
    - Ensure route accessible at `http://localhost:5000/pts-renamer/`
    - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5_

  - [ ] 7.2 Create HTML templates and user interface
    - Implement `pts_rename_main.html` with modern web interface
    - Support drag-and-drop upload, real-time progress, and preview
    - Include all processing options (rename, QC, directories)
    - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5_

  - [ ] 7.3 Implement JavaScript frontend functionality
    - Create `pts_rename_upload.js` for file upload and progress tracking
    - Implement `pts_rename_processor.js` for processing configuration
    - Add real-time status updates and result display
    - _Requirements: 3.2, 3.3, 3.4, 3.5_

- [ ] 8. Prepare FastAPI endpoints (future implementation)
  - [ ] 8.1 Create FastAPI route definitions
    - Implement `pts_rename_fastapi_routes.py` with OpenAPI documentation
    - Mirror Flask functionality with proper async support
    - _Requirements: 11.1, 11.2, 11.3_

  - [ ] 8.2 Set up API documentation and testing
    - Configure interactive API documentation
    - Create API testing interface and examples
    - _Requirements: 11.1, 11.2, 11.3, 11.4, 11.5_

- [ ] 9. Implement security and validation
  - [ ] 9.1 Add file upload security measures
    - Implement file type validation and malicious content scanning
    - Enforce size limits and timeout restrictions
    - _Requirements: 6.1, 6.2, 6.3_

  - [ ] 9.2 Create authentication and authorization
    - Integrate with existing authentication system
    - Implement proper access controls for file operations
    - _Requirements: 6.4, 6.5_

- [ ] 10. Implement error handling and logging
  - [ ] 10.1 Create comprehensive error handling
    - Implement standardized error responses and categories
    - Handle upload, processing, and system errors gracefully
    - _Requirements: 6.5, 7.3_

  - [ ] 10.2 Integrate with existing logging and monitoring
    - Use existing logging infrastructure for audit trails
    - Integrate with unified monitoring dashboard
    - _Requirements: 1.4, 7.1, 7.2_

- [ ] 11. Create comprehensive testing suite
  - [ ] 11.1 Implement unit tests for all services
    - Create tests for processor, QC generator, directory manager
    - Test presenter logic and service orchestration
    - _Requirements: All requirements validation_

  - [ ] 11.2 Create integration tests
    - Test API endpoints and Dramatiq task integration
    - Verify file processing workflows end-to-end
    - _Requirements: 2.1-2.5, 4.1-4.7, 7.1-7.5_

  - [ ] 11.3 Implement E2E web interface tests
    - Use Playwright for complete user workflow testing
    - Test upload, processing, and download flows
    - _Requirements: 3.1-3.5_

- [ ] 12. Set up deployment and configuration
  - [ ] 12.1 Configure service integration
    - Register with existing service discovery
    - Set up configuration management
    - _Requirements: 7.1, 7.4_

  - [ ] 12.2 Implement data management and cleanup
    - Set up automatic file cleanup policies
    - Configure retention periods and storage limits
    - _Requirements: 9.1, 9.2, 9.3, 9.4, 9.5_

- [ ] 13. Create documentation and migration guide
  - [ ] 13.1 Create user documentation
    - Document web interface usage and API endpoints
    - Create troubleshooting and FAQ sections
    - _Requirements: 11.1, 11.2_

  - [ ] 13.2 Document Vue.js + FastAPI migration path
    - Create migration strategy and timeline
    - Document API compatibility and frontend transition
    - _Requirements: Future migration preparation_